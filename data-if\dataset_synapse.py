import os
import random
import h5py
import numpy as np
import torch
from scipy import ndimage
from scipy.ndimage.interpolation import zoom
from torch.utils.data import Dataset
from PIL import Image
import glob


def random_rot_flip(image, label):
    k = np.random.randint(0, 4)
    image = np.rot90(image, k)
    label = np.rot90(label, k)
    axis = np.random.randint(0, 2)
    image = np.flip(image, axis=axis).copy()
    label = np.flip(label, axis=axis).copy()
    return image, label


def random_rotate(image, label):
    angle = np.random.randint(-20, 20)
    image = ndimage.rotate(image, angle, order=0, reshape=False)
    label = ndimage.rotate(label, angle, order=0, reshape=False)
    return image, label


class RandomGenerator(object):
    def __init__(self, output_size):
        self.output_size = output_size

    def __call__(self, sample):
        image, label = sample['image'], sample['label']

        if random.random() > 0.5:
            image, label = random_rot_flip(image, label)
        elif random.random() > 0.5:
            image, label = random_rotate(image, label)
        x, y = image.shape
        if x != self.output_size[0] or y != self.output_size[1]:
            image = zoom(image, (self.output_size[0] / x, self.output_size[1] / y), order=3)  # why not 3?
            label = zoom(label, (self.output_size[0] / x, self.output_size[1] / y), order=0)
        image = torch.from_numpy(image.astype(np.float32)).unsqueeze(0)
        label = torch.from_numpy(label.astype(np.float32))
        sample = {'image': image, 'label': label.long()}
        return sample


class Synapse_dataset(Dataset):
    def __init__(self, base_dir, list_dir, split, transform=None):
        self.transform = transform  # using transform in torch!
        self.split = split
        self.sample_list = open(os.path.join(list_dir, self.split+'.txt')).readlines()
        self.data_dir = base_dir

    def __len__(self):
        return len(self.sample_list)

    def __getitem__(self, idx):
        if self.split == "train":
            slice_name = self.sample_list[idx].strip('\n')
            data_path = os.path.join(self.data_dir, slice_name+'.npz')
            data = np.load(data_path)
            image, label = data['image'], data['label']
        else:
            vol_name = self.sample_list[idx].strip('\n')
            filepath = self.data_dir + "/{}.npy.h5".format(vol_name)
            data = h5py.File(filepath)
            image, label = data['image'][:], data['label'][:]

        sample = {'image': image, 'label': label}
        if self.transform:
            sample = self.transform(sample)
        sample['case_name'] = self.sample_list[idx].strip('\n')
        return sample


class DataIF_dataset(Dataset):
    def __init__(self, base_dir, image_type='rgb', split='train', transform=None):
        """
        Dataset for data-if folder
        Args:
            base_dir: path to data-if folder
            image_type: 'rgb' for visible images or 'ir' for infrared images
            split: 'train' or 'test' (for now we'll use all data as train)
            transform: data augmentation transforms
        """
        self.transform = transform
        self.split = split
        self.image_type = image_type
        self.base_dir = base_dir

        # Define paths based on image type
        if image_type == 'rgb':
            self.image_dir = os.path.join(base_dir, '01-Visible images')
        elif image_type == 'ir':
            self.image_dir = os.path.join(base_dir, '02-Infrared images')
        else:
            raise ValueError("image_type must be 'rgb' or 'ir'")

        self.label_dir = os.path.join(base_dir, '04-Ground truth')

        # Get all image files
        image_extensions = ['*.png', '*.jpg', '*.jpeg']
        self.image_files = []
        for ext in image_extensions:
            self.image_files.extend(glob.glob(os.path.join(self.image_dir, ext)))

        self.image_files.sort()

        # Filter to only include files that have corresponding ground truth
        self.valid_files = []
        for img_path in self.image_files:
            img_name = os.path.splitext(os.path.basename(img_path))[0]
            label_path = os.path.join(self.label_dir, img_name + '.jpg')
            if os.path.exists(label_path):
                self.valid_files.append((img_path, label_path))

        print(f"Found {len(self.valid_files)} valid {image_type.upper()} image-label pairs")

    def __len__(self):
        return len(self.valid_files)

    def __getitem__(self, idx):
        img_path, label_path = self.valid_files[idx]

        # Load image
        image = Image.open(img_path).convert('RGB')
        image = np.array(image)

        # Load label (ground truth)
        label = Image.open(label_path).convert('L')  # Convert to grayscale
        label = np.array(label)

        # Convert to grayscale if needed (for consistency)
        if len(image.shape) == 3:
            image = np.mean(image, axis=2)  # Convert RGB to grayscale

        # Normalize image to [0, 1]
        image = image.astype(np.float32) / 255.0

        # Normalize label (assuming binary segmentation)
        label = (label > 128).astype(np.float32)  # Binary threshold

        sample = {'image': image, 'label': label}
        if self.transform:
            sample = self.transform(sample)

        # Get case name from filename
        case_name = os.path.splitext(os.path.basename(img_path))[0]
        sample['case_name'] = case_name

        return sample
