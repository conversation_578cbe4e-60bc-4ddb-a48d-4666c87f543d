#!/usr/bin/env python3
"""
Quick Model Comparison Script
Runs all 4 models (U-Net, CSWin-UNet, ResU-Net, TransU-Net) for both RGB and IR datasets
"""

import os
import sys
import argparse
import os
import sys
import time
import json
import argparse
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import sys
import os
sys.path.append('./cswin-cbam-unet-ir-segmentation')
sys.path.append(os.path.join(os.getcwd(), 'cswin-cbam-unet-ir-segmentation'))
from pytorch_unet_trainer import PyTorchUNetTrainer
from pytorch_cswinunet_trainer import PyTorchCSWinUNetTrainer
from pytorch_resunet_trainer import PyTorchResUNetTrainer
from pytorch_transunet_trainer import PyTorchTransUNetTrainer
from pytorch_cbam_cswinunet_trainer import PyTorchCSWinUNetTrainer

class ModelComparator:
    def __init__(self, data_dir='./cswin-cbam-unet-ir-segmentation/data-if', epochs=10, batch_size=8, lr=0.001):
        self.data_dir = data_dir
        self.epochs = epochs
        self.batch_size = batch_size
        self.lr = lr
        self.results = {}
        self.setup_output_dir()
        
    def setup_output_dir(self):
        """Setup output directory for comparison results"""
        self.output_dir = f'model_comparison_{datetime.now().strftime("%Y%m%d_%H%M%S")}'
        os.makedirs(self.output_dir, exist_ok=True)
        
    def log_message(self, message):
        """Log message with timestamp"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] {message}")
        
        # Also save to log file
        with open(f'{self.output_dir}/comparison_log.txt', 'a', encoding='utf-8') as f:
            f.write(f"[{timestamp}] {message}\n")
    
    def train_unet_models(self, image_type):
        """Train U-Net models for given image type"""
        self.log_message(f"🔥 Training U-Net models for {image_type.upper()} dataset...")
        
        trainer = PyTorchUNetTrainer(
            data_dir=self.data_dir,
            image_type=image_type,
            epochs=self.epochs,
            batch_size=self.batch_size,
            lr=self.lr
        )
        
        start_time = time.time()
        success = trainer.run_complete_training()
        training_time = time.time() - start_time
        
        if success:
            # Get best IoU from training history
            best_iou = max(trainer.training_history['val_iou'])
            best_dice = max(trainer.training_history['val_dice'])
            
            self.results[f'unet_{image_type}'] = {
                'model': 'U-Net',
                'image_type': image_type,
                'best_iou': best_iou,
                'best_dice': best_dice,
                'training_time': training_time,
                'epochs': self.epochs,
                'success': True
            }
            
            self.log_message(f"✅ U-Net {image_type.upper()} - IoU: {best_iou:.4f}, Dice: {best_dice:.4f}")
        else:
            self.results[f'unet_{image_type}'] = {
                'model': 'U-Net',
                'image_type': image_type,
                'success': False
            }
            self.log_message(f"❌ U-Net {image_type.upper()} training failed!")
    
    def train_cswinunet_models(self, image_type):
        """Train CSWin-UNet models for given image type"""
        self.log_message(f"🔥 Training CSWin-UNet models for {image_type.upper()} dataset...")
        
        trainer = PyTorchCSWinUNetTrainer(
            data_dir=self.data_dir,
            image_type=image_type,
            epochs=self.epochs,
            batch_size=self.batch_size,
            lr=self.lr
        )
        
        start_time = time.time()
        success = trainer.run_complete_training()
        training_time = time.time() - start_time
        
        if success:
            # Get best IoU from training history
            best_iou = max(trainer.training_history['val_iou'])
            best_dice = max(trainer.training_history['val_dice'])
            
            self.results[f'cswinunet_{image_type}'] = {
                'model': 'CSWin-UNet',
                'image_type': image_type,
                'best_iou': best_iou,
                'best_dice': best_dice,
                'training_time': training_time,
                'epochs': self.epochs,
                'success': True
            }
            
            self.log_message(f"✅ CSWin-UNet {image_type.upper()} - IoU: {best_iou:.4f}, Dice: {best_dice:.4f}")
        else:
            self.results[f'cswinunet_{image_type}'] = {
                'model': 'CSWin-UNet',
                'image_type': image_type,
                'success': False
            }
            self.log_message(f"❌ CSWin-UNet {image_type.upper()} training failed!")
    
    def train_resunet_models(self, image_type):
        """Train ResU-Net models for given image type"""
        self.log_message(f"🔥 Training ResU-Net models for {image_type.upper()} dataset...")

        trainer = PyTorchResUNetTrainer(
            data_dir=self.data_dir,
            image_type=image_type,
            epochs=self.epochs,
            batch_size=self.batch_size,
            lr=self.lr
        )

        start_time = time.time()
        success = trainer.run_complete_training()
        training_time = time.time() - start_time

        if success:
            # Get best IoU from training history
            best_iou = max(trainer.training_history['val_iou'])
            best_dice = max(trainer.training_history['val_dice'])

            self.results[f'resunet_{image_type}'] = {
                'model': 'ResU-Net',
                'image_type': image_type,
                'best_iou': best_iou,
                'best_dice': best_dice,
                'training_time': training_time,
                'epochs': self.epochs,
                'success': True
            }

            self.log_message(f"✅ ResU-Net {image_type.upper()} - IoU: {best_iou:.4f}, Dice: {best_dice:.4f}")
        else:
            self.results[f'resunet_{image_type}'] = {
                'model': 'ResU-Net',
                'image_type': image_type,
                'success': False
            }
            self.log_message(f"❌ ResU-Net {image_type.upper()} training failed!")

    def train_transunet_models(self, image_type):
        """Train TransU-Net models for given image type"""
        self.log_message(f"🔥 Training TransU-Net models for {image_type.upper()} dataset...")

        trainer = PyTorchTransUNetTrainer(
            data_dir=self.data_dir,
            image_type=image_type,
            epochs=self.epochs,
            batch_size=self.batch_size,
            lr=self.lr
        )

        start_time = time.time()
        success = trainer.run_complete_training()
        training_time = time.time() - start_time

        if success:
            # Get best IoU from training history
            best_iou = max(trainer.training_history['val_iou'])
            best_dice = max(trainer.training_history['val_dice'])

            self.results[f'transunet_{image_type}'] = {
                'model': 'TransU-Net',
                'image_type': image_type,
                'best_iou': best_iou,
                'best_dice': best_dice,
                'training_time': training_time,
                'epochs': self.epochs,
                'success': True
            }

            self.log_message(f"✅ TransU-Net {image_type.upper()} - IoU: {best_iou:.4f}, Dice: {best_dice:.4f}")
        else:
            self.results[f'transunet_{image_type}'] = {
                'model': 'TransU-Net',
                'image_type': image_type,
                'success': False
            }
            self.log_message(f"❌ TransU-Net {image_type.upper()} training failed!")
    
    def run_complete_comparison(self):
        """Run complete model comparison for both RGB and IR datasets"""
        self.log_message("🚀 Starting Complete Model Comparison")
        self.log_message("="*80)
        
        total_start_time = time.time()
        
        # Train all models for both image types
        for image_type in ['rgb', 'ir']:
            self.log_message(f"\n{'='*20} {image_type.upper()} DATASET TRAINING {'='*20}")

            # Train U-Net
            self.train_unet_models(image_type)

            # Train CSWin-UNet
            self.train_cswinunet_models(image_type)

            # Train ResU-Net
            self.train_resunet_models(image_type)

            # Train TransU-Net
            self.train_transunet_models(image_type)

            self.log_message(f"✅ Completed all models for {image_type.upper()} dataset")
        
        total_time = time.time() - total_start_time
        
        # Generate comparison results
        self.generate_comparison_plots()
        self.generate_comparison_report()
        self.save_results()
        
        self.log_message("="*80)
        self.log_message(f"🎉 Complete model comparison finished in {total_time/3600:.2f} hours")
        self.log_message(f"📁 Results saved to: {self.output_dir}/")
    
    def generate_comparison_plots(self):
        """Generate comprehensive comparison plots"""
        self.log_message("📊 Generating comparison plots...")
        
        # Prepare data for plotting
        models = []
        rgb_ious = []
        ir_ious = []
        rgb_times = []
        ir_times = []
        
        model_names = ['U-Net', 'CSWin-UNet', 'ResU-Net', 'TransU-Net']
        model_keys = ['unet', 'cswinunet', 'resunet', 'transunet']
        
        for i, key in enumerate(model_keys):
            models.append(model_names[i])
            
            # RGB results
            rgb_key = f'{key}_rgb'
            if rgb_key in self.results and self.results[rgb_key]['success']:
                rgb_ious.append(self.results[rgb_key]['best_iou'])
                rgb_times.append(self.results[rgb_key]['training_time'] / 3600)  # Convert to hours
            else:
                rgb_ious.append(0)
                rgb_times.append(0)
            
            # IR results
            ir_key = f'{key}_ir'
            if ir_key in self.results and self.results[ir_key]['success']:
                ir_ious.append(self.results[ir_key]['best_iou'])
                ir_times.append(self.results[ir_key]['training_time'] / 3600)  # Convert to hours
            else:
                ir_ious.append(0)
                ir_times.append(0)
        
        # Create comparison plots
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        
        # IoU Comparison
        x = np.arange(len(models))
        width = 0.35
        
        axes[0,0].bar(x - width/2, rgb_ious, width, label='RGB', color='skyblue', alpha=0.8)
        axes[0,0].bar(x + width/2, ir_ious, width, label='IR', color='lightcoral', alpha=0.8)
        axes[0,0].set_xlabel('Models')
        axes[0,0].set_ylabel('Best IoU Score')
        axes[0,0].set_title('Model Performance Comparison - IoU Scores')
        axes[0,0].set_xticks(x)
        axes[0,0].set_xticklabels(models, rotation=45)
        axes[0,0].legend()
        axes[0,0].grid(True, alpha=0.3)
        axes[0,0].set_ylim(0, 1)
        
        # Add value labels on bars
        for i, (rgb_val, ir_val) in enumerate(zip(rgb_ious, ir_ious)):
            if rgb_val > 0:
                axes[0,0].text(i - width/2, rgb_val + 0.01, f'{rgb_val:.3f}', 
                              ha='center', va='bottom', fontsize=9)
            if ir_val > 0:
                axes[0,0].text(i + width/2, ir_val + 0.01, f'{ir_val:.3f}', 
                              ha='center', va='bottom', fontsize=9)
        
        # Training Time Comparison
        axes[0,1].bar(x - width/2, rgb_times, width, label='RGB', color='lightgreen', alpha=0.8)
        axes[0,1].bar(x + width/2, ir_times, width, label='IR', color='orange', alpha=0.8)
        axes[0,1].set_xlabel('Models')
        axes[0,1].set_ylabel('Training Time (hours)')
        axes[0,1].set_title('Training Time Comparison')
        axes[0,1].set_xticks(x)
        axes[0,1].set_xticklabels(models, rotation=45)
        axes[0,1].legend()
        axes[0,1].grid(True, alpha=0.3)
        
        # Performance vs Time scatter plot
        all_ious = rgb_ious + ir_ious
        all_times = rgb_times + ir_times
        colors = ['blue']*len(models) + ['red']*len(models)
        labels = [f'{m} (RGB)' for m in models] + [f'{m} (IR)' for m in models]
        
        for i, (iou, time, color, label) in enumerate(zip(all_ious, all_times, colors, labels)):
            if iou > 0 and time > 0:
                axes[1,0].scatter(time, iou, c=color, s=100, alpha=0.7, label=label if i < 8 else "")
        
        axes[1,0].set_xlabel('Training Time (hours)')
        axes[1,0].set_ylabel('Best IoU Score')
        axes[1,0].set_title('Performance vs Training Time')
        axes[1,0].grid(True, alpha=0.3)
        axes[1,0].legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        
        # Model ranking
        successful_results = [(k, v) for k, v in self.results.items() if v['success']]
        successful_results.sort(key=lambda x: x[1]['best_iou'], reverse=True)
        
        if successful_results:
            top_models = [f"{r[1]['model']} ({r[1]['image_type'].upper()})" for r in successful_results[:8]]
            top_ious = [r[1]['best_iou'] for r in successful_results[:8]]
            
            axes[1,1].barh(range(len(top_models)), top_ious, color='purple', alpha=0.7)
            axes[1,1].set_yticks(range(len(top_models)))
            axes[1,1].set_yticklabels(top_models)
            axes[1,1].set_xlabel('IoU Score')
            axes[1,1].set_title('Model Ranking (Best IoU)')
            axes[1,1].grid(True, alpha=0.3)
            
            # Add value labels
            for i, val in enumerate(top_ious):
                axes[1,1].text(val + 0.01, i, f'{val:.3f}', va='center', fontsize=9)
        
        plt.tight_layout()
        plt.savefig(f'{self.output_dir}/model_comparison_plots.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        self.log_message(f"📊 Comparison plots saved to: {self.output_dir}/model_comparison_plots.png")
    
    def generate_comparison_report(self):
        """Generate detailed comparison report"""
        self.log_message("📄 Generating comparison report...")
        
        report = f"""
# Complete Model Comparison Report
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Training Configuration
- Data Directory: {self.data_dir}
- Epochs: {self.epochs}
- Batch Size: {self.batch_size}
- Learning Rate: {self.lr}

## Model Performance Summary

### RGB Dataset Results:
"""
        
        # RGB results
        for model_type in ['unet', 'cswinunet', 'resunet', 'transunet']:
            key = f'{model_type}_rgb'
            if key in self.results:
                result = self.results[key]
                if result['success']:
                    report += f"""
- **{result['model']}**:
  - Best IoU: {result['best_iou']:.4f}
  - Best Dice: {result['best_dice']:.4f}
  - Training Time: {result['training_time']/3600:.2f} hours
"""
                else:
                    report += f"""
- **{result['model']}**: FAILED
"""

        report += "\n### IR Dataset Results:\n"

        # IR results
        for model_type in ['unet', 'cswinunet', 'resunet', 'transunet']:
            key = f'{model_type}_ir'
            if key in self.results:
                result = self.results[key]
                if result['success']:
                    report += f"""
- **{result['model']}**:
  - Best IoU: {result['best_iou']:.4f}
  - Best Dice: {result['best_dice']:.4f}
  - Training Time: {result['training_time']/3600:.2f} hours
"""
                else:
                    report += f"""
- **{result['model']}**: FAILED
"""
        
        # Best performing models
        successful_results = [(k, v) for k, v in self.results.items() if v['success']]
        if successful_results:
            successful_results.sort(key=lambda x: x[1]['best_iou'], reverse=True)
            
            report += f"""
## Top Performing Models (by IoU):

"""
            for i, (key, result) in enumerate(successful_results[:5], 1):
                report += f"{i}. **{result['model']} ({result['image_type'].upper()})**: {result['best_iou']:.4f} IoU\n"
        
        # Analysis and recommendations
        report += f"""

## Analysis and Recommendations:

### Performance Analysis:
- Total models trained: {len(self.results)}
- Successful trainings: {len([r for r in self.results.values() if r['success']])}
- Failed trainings: {len([r for r in self.results.values() if not r['success']])}

### Dataset Comparison:
"""
        
        # Compare RGB vs IR performance
        rgb_results = [v for k, v in self.results.items() if 'rgb' in k and v['success']]
        ir_results = [v for k, v in self.results.items() if 'ir' in k and v['success']]
        
        if rgb_results and ir_results:
            avg_rgb_iou = np.mean([r['best_iou'] for r in rgb_results])
            avg_ir_iou = np.mean([r['best_iou'] for r in ir_results])
            
            report += f"""
- Average RGB IoU: {avg_rgb_iou:.4f}
- Average IR IoU: {avg_ir_iou:.4f}
- Better dataset: {'RGB' if avg_rgb_iou > avg_ir_iou else 'IR'}
"""
        
        report += """

### Model Architecture Comparison:
"""
        
        # Compare model architectures
        model_performance = {}
        for key, result in self.results.items():
            if result['success']:
                model_name = result['model']
                if model_name not in model_performance:
                    model_performance[model_name] = []
                model_performance[model_name].append(result['best_iou'])
        
        for model, ious in model_performance.items():
            avg_iou = np.mean(ious)
            report += f"- **{model}**: Average IoU {avg_iou:.4f} ({len(ious)} successful runs)\n"
        
        # Save report
        with open(f'{self.output_dir}/comparison_report.md', 'w', encoding='utf-8') as f:
            f.write(report)
        
        self.log_message(f"📄 Comparison report saved to: {self.output_dir}/comparison_report.md")
    
    def save_results(self):
        """Save all results to JSON file"""
        results_file = f'{self.output_dir}/comparison_results.json'
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2)
        
        self.log_message(f"💾 Results saved to: {results_file}")


def main():
    """Main function to run complete model comparison"""
    parser = argparse.ArgumentParser(description='Complete Model Comparison for IR and RGB Segmentation')
    parser.add_argument('--data_dir', type=str, default='./cswin-cbam-unet-ir-segmentation/data-if', help='Data directory')
    parser.add_argument('--epochs', type=int, default=50, help='Number of epochs for each model')
    parser.add_argument('--batch_size', type=int, default=8, help='Batch size')
    parser.add_argument('--lr', type=float, default=0.001, help='Learning rate')
    
    args = parser.parse_args()
    
    print("🎯 Complete Model Comparison")
    print("="*80)
    print("Models to compare:")
    print("1. U-Net (RGB & IR)")
    print("2. CSWin-UNet (RGB & IR)")
    print("3. ResU-Net (RGB & IR)")
    print("4. TransU-Net (RGB & IR)")
    print("="*80)
    print(f"Configuration:")
    print(f"- Data Directory: {args.data_dir}")
    print(f"- Epochs per model: {args.epochs}")
    print(f"- Batch Size: {args.batch_size}")
    print(f"- Learning Rate: {args.lr}")
    print("="*80)
    
    # Create comparator and run comparison
    comparator = ModelComparator(
        data_dir=args.data_dir,
        epochs=args.epochs,
        batch_size=args.batch_size,
        lr=args.lr
    )
    
    comparator.run_complete_comparison()
    
    print("\n🎉 Model comparison completed successfully!")
    print(f"📁 Check results in: {comparator.output_dir}/")


if __name__ == "__main__":
    main()
def main():
    """Main function to run model comparison with configurable parameters"""
    parser = argparse.ArgumentParser(description='Run Complete Model Comparison')
    parser.add_argument('--epochs', type=int, default=5, help='Number of epochs (default: 100)')
    parser.add_argument('--batch_size', type=int, default=16, help='Batch size (default: 4)')
    parser.add_argument('--lr', type=float, default=0.0001, help='Learning rate (default: 0.001)')
    parser.add_argument('--data_dir', type=str, default='./cswin-cbam-unet-ir-segmentation/data-if', help='Data directory')
    
    args = parser.parse_args()
    
    print("🎯 Complete Model Comparison Script")
    print("="*80)
    print("This script will train and compare 4 different models:")
    print("1. U-Net (Standard U-Net architecture)")
    print("2. CSWin-UNet (Cross-Shaped Window Transformer + U-Net)")
    print("3. ResU-Net (Residual U-Net)")
    print("4. TransU-Net (Vision Transformer + U-Net)")
    print()
    print("Each model will be trained on both RGB and IR datasets")
    print("="*80)
    print(f"Configuration:")
    print(f"- Data Directory: {args.data_dir}")
    print(f"- Epochs per model: {args.epochs}")
    print(f"- Batch Size: {args.batch_size}")
    print(f"- Learning Rate: {args.lr}")
    print("="*80)
    
    # Check if data directory exists
    if not os.path.exists(args.data_dir):
        print(f"❌ Error: Data directory '{args.data_dir}' does not exist!")
        print("Please make sure the data directory path is correct.")
        sys.exit(1)
    
    # Check if required subdirectories exist
    required_dirs = ['01-Visible images', '02-Infrared images', '04-Ground truth']
    for req_dir in required_dirs:
        full_path = os.path.join(args.data_dir, req_dir)
        if not os.path.exists(full_path):
            print(f"❌ Error: Required subdirectory '{req_dir}' not found in data directory!")
            sys.exit(1)
    
    print("✅ Data directory structure verified!")
    print()
    
    # Confirm before starting
    response = input("Do you want to start the model comparison? (y/N): ")
    if response.lower() not in ['y', 'yes']:
        print("Model comparison cancelled.")
        sys.exit(0)
    
    # Create comparator and run comparison
    try:
        comparator = ModelComparator(
            data_dir=args.data_dir,
            epochs=args.epochs,
            batch_size=args.batch_size,
            lr=args.lr
        )
        
        print("\n🚀 Starting model comparison...")
        comparator.run_complete_comparison()
        
        print("\n🎉 Model comparison completed successfully!")
        print(f"📁 Check results in: {comparator.output_dir}/")
        print("\nGenerated files:")
        print("- comparison_log.txt: Detailed training log")
        print("- comparison_results.json: Raw results data")
        print("- comparison_report.md: Comprehensive analysis report")
        print("- model_comparison_plots.png: Performance visualization")
        
    except KeyboardInterrupt:
        print("\n⚠️ Model comparison interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Error during model comparison: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
